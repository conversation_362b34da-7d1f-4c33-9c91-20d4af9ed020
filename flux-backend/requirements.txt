APScheduler==3.10.4
asyncmy==0.2.9
DateTime==5.5
fastapi[all]==0.115.0
loguru==0.7.2
openpyxl==3.1.5
pandas==2.2.3
bcrypt==4.0.1
passlib[bcrypt]==1.7.4
Pillow==10.4.0
psutil==6.0.0
pydantic-validation-decorator==0.1.4
PyJWT[crypto]==2.8.0
PyMySQL==1.1.1
redis==5.0.7
requests==2.32.3
SQLAlchemy[asyncio]==2.0.31
user-agents==2.2.0

starlette==0.38.6
pydantic==2.9.2
uvicorn==0.34.0
python-dotenv==1.0.1
pydantic-settings==2.6.1
jinja2==3.1.6
openai==1.72.0
markdown==3.7
oss2==2.18.4
tinify==1.6.0
beautifulsoup4==4.12.3
granian==2.2.0
uvicorn_worker==0.3.0

mcp==1.6.0
httpx==0.28.1
click==8.1.8
watchfiles==1.0.4
pycryptodome==3.21.0