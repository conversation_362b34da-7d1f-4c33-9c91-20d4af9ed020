# -*- coding:utf-8 -*-
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import List, Literal, Optional, Union
from module_admin.annotation.pydantic_annotation import as_query


class {{ tableName|snake_to_pascal_case }}{% if subTable %}Base{% endif %}Model(BaseModel):
    """
    表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)
    {% for column in columns %}
    {{ column.columnName }}: Optional[{{ column.pythonType }}] =  Field(default=None, description='{{ column.columnComment }}')
    {% if column.queryType == 'BETWEEN' %}
    begin_{{ column.columnName }}: Optional[{{ column.pythonType }}] =  Field(default=None, description='{{ column.columnComment }}最小值')
    {% endif %}
    {% if column.queryType == 'BETWEEN' %}
    end_{{ column.columnName }}: Optional[{{ column.pythonType }}] =  Field(default=None, description='{{ column.columnComment }}最大值')
    {% endif %}
    {% endfor %}


{% if subTable %}
class {{ tableName|snake_to_pascal_case }}Model({{ tableName|snake_to_pascal_case }}BaseModel):
    {{ subTableName }}_list: Optional[List['{{ subTable.table_name | snake_to_pascal_case  }}Model']] = Field(default=None, description='子表列信息')
{% endif %}

@as_query
class {{ tableName|snake_to_pascal_case }}PageModel({{ tableName|snake_to_pascal_case }}{% if subTable %}Base{% endif %}Model):
    """
    分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


{% if subTable %}
class {{ subTable.table_name | snake_to_pascal_case }}Model(BaseModel):
    """
    {{ subTable.function_name }}表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    {% for sub_column in subTable.columns %}
    {{ sub_column.column_name }}: Optional[{{ sub_column.python_type }}] = Field(default=None, description='{{ sub_column.column_comment}}')
    {% endfor %}

    {% for sub_column in subTable.columns %}
    {% if sub_column.required %}
    {% set parentheseIndex = sub_column.column_comment.find("（") %}
    {% set comment = sub_column.column_comment[:parentheseIndex] if parentheseIndex != -1 else sub_column.column_comment %}
    @NotBlank(field_name='{{ sub_column.column_name }}', message='{{ comment }}不能为空')
    def get_{{ sub_column.column_name }}(self):
        return self.{{ sub_column.column_name }}
    {% if not loop.last %}{{ "\n" }}{% endif %}
    {% endif %}
    {% endfor %}

{% endif %}

