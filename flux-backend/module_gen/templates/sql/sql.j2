-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{functionName}}', '{{parentMenuId}}', '1', '{{moduleName}}_{{businessName}}', '{{moduleName}}/{{businessName}}/index', 1, 0, 'C', '0', '0', '{{permissionPrefix}}:list', '#', 'admin', sysdate(), '', null, '{{functionName}}菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{functionName}}查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', '{{permissionPrefix}}:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{functionName}}新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', '{{permissionPrefix}}:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{functionName}}修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', '{{permissionPrefix}}:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{functionName}}删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', '{{permissionPrefix}}:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{functionName}}导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', '{{permissionPrefix}}:export',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('{{functionName}}导入', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', '{{permissionPrefix}}:import',       '#', 'admin', sysdate(), '', null, '');


{% for column in columns %}
{% set pythonField = column.pythonField | snake_to_camel %}
{% set parentheseIndex = column.columnComment.find("（") %}
{% set comment = column.columnComment[:parentheseIndex] if parentheseIndex != -1 else column.columnComment %}
{% if column.isList %}
INSERT INTO `sys_table` (`table_name`, `field_name`, `prop`, `label`, `sequence`) VALUES ('{{ tableName }}', '{{ column.pythonField }}', '{{ pythonField }}', '{{ comment }}', {{ loop.index0 }});
{% endif %}
{% endfor %}
INSERT INTO `sys_table` (`table_name`, `field_name`, `prop`, `label`, `sequence`, `fixed`) VALUES ('{{ tableName }}', 'operate', 'operate', '操作', {{ columns|length }}, '2');
