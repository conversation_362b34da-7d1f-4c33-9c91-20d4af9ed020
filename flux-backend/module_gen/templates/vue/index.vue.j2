<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      {% for column in columns %}
        {% if column.isQuery == "1" %}
          {% set dictType = column.dictType %}
          {% set parentheseIndex = column.columnComment.find("（") %}
          {% set comment = column.columnComment[:parentheseIndex] if parentheseIndex != -1 else column.columnComment %}

          {% if column.htmlType == "input" %}
            <el-form-item label="{{ comment }}" prop="{{ column.pythonField  | snake_to_camel }}">
              <el-input
                v-model="queryParams.{{ column.pythonField | snake_to_camel }}"
                placeholder="请输入{{ comment }}"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          {% elif (column.htmlType == "select" or column.htmlType == "radio") and dictType != "" %}
            <el-form-item label="{{ comment }}" prop="{{ column.pythonField  | snake_to_camel  }}">
              <el-select
                  v-model="queryParams.{{ column.pythonField | snake_to_camel  }}"
                  placeholder="请选择{{ comment }}"
                  style="width: 180px"
                  clearable>
                <el-option
                  v-for="dict in {{ dictType }}"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          {% elif (column.htmlType == "select" or column.htmlType == "radio") and dictType %}
            <el-form-item label="{{ comment }}" prop="{{ column.pythonField | snake_to_camel  }}">
              <el-select v-model="queryParams.{{ column.pythonField | snake_to_camel  }}" placeholder="请选择{{ comment }}" clearable>
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          {% elif column.htmlType == "datetime" and column.queryType != "BETWEEN" %}
            <el-form-item label="{{ comment }}" prop="{{ column.pythonField  | snake_to_camel }}">
              <el-date-picker clearable
                v-model="queryParams.{{ column.pythonField | snake_to_camel  }}"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择{{ comment }}">
              </el-date-picker>
            </el-form-item>
          {% elif column.htmlType == "datetime" and column.queryType == "BETWEEN" %}
            <el-form-item label="{{ comment }}" style="width: 308px">
              <el-date-picker
                v-model="daterange{{ column.pythonField | snake_to_pascal_case }}"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          {% endif %}
        {% endif %}
      {% endfor %}
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-card class="base-table" ref="fullTable">
            <TableSetup
                ref="tSetup"
                @onStripe="onStripe"
                @onRefresh="onRefresh"
                @onChange="onChange"
                @onfullTable="onfullTable"
                @onSearchChange="onSearchChange"
                :columns="columns"
                :isTable="isTable"
            >
                <template v-slot:operate>
                    <el-button
                      type="primary"
                      plain
                      icon="Plus"
                      @click="handleAdd"
                      v-hasPermi="['{{ moduleName }}:{{ businessName }}:add']"
                    >新增</el-button>
                    <el-button
                      type="success"
                      plain
                      icon="Edit"
                      :disabled="single"
                      @click="handleUpdate"
                      v-hasPermi="['{{ moduleName }}:{{ businessName }}:edit']"
                    >修改</el-button>
                    <el-button
                      type="danger"
                      plain
                      icon="Delete"
                      :disabled="multiple"
                      @click="handleDelete"
                      v-hasPermi="['{{ moduleName }}:{{ businessName }}:remove']"
                    >删除</el-button>
                  <el-button
                        type="primary"
                        plain
                        icon="Upload"
                        @click="handleImport"
                        v-hasPermi="['{{ moduleName }}:{{ businessName }}:import']"
                        >导入</el-button
                    >
                    <el-button
                      type="warning"
                      plain
                      icon="Download"
                      @click="handleExport"
                      v-hasPermi="['{{ moduleName }}:{{ businessName }}:export']"
                    >导出</el-button>
                </template>
            </TableSetup>
            <auto-table
                ref="multipleTable"
                class="mytable"
                :tableData="{{ businessName }}List"
                :columns="columns"
                :loading="loading"
                :stripe="stripe"
                :tableHeight="tableHeight"
                @onColumnWidthChange="onColumnWidthChange"
                @onSelectionChange="handleSelectionChange"
            >
              {% for column in columns %}
                {% set pythonField = column.pythonField | snake_to_camel %}
                {% set parentheseIndex = column.columnComment.find("（") %}
                {% set comment = column.columnComment[:parentheseIndex] if parentheseIndex != -1 else column.columnComment %}

                {% if column.isList and column.htmlType == "datetime" %}
                    <template #{{ pythonField }}="{ row }">
                      <span>{% raw %}{{{% endraw %} parseTime(row.{{ pythonField }}, '{y}-{m}-{d}') {% raw %}}}{% endraw %}</span>
                    </template>
                {% elif column.isList == "1" and column.htmlType == "imageUpload" %}
                    <template #{{ pythonField }}="{ row }">
                      <image-preview :src="fullUrl(row.{{ pythonField }})"  v-if="row.{{ pythonField }}" :width="50" :height="50"/>
                    </template>
                {% elif column.isList == "1" and column.dictType != "" %}
                    <template #{{ pythonField }}="{ row }">
                      {% if column.htmlType == "checkbox" %}
                        <dict-tag :options="{{ column.dictType }}" :value="row.{{ pythonField }} ? row.{{ pythonField }}.split(',') : []"/>
                      {% else %}
                        <dict-tag :options="{{ column.dictType }}" :value="row.{{ pythonField }}"/>
                      {% endif %}
                    </template>
                {% endif %}
              {% endfor %}
                <template #operate="{ row }">
                  <el-button link type="primary" icon="Edit" @click="handleUpdate(row)" v-hasPermi="['{{ moduleName }}:{{ businessName }}:edit']">修改</el-button>
                  <el-button link type="primary" icon="Delete" @click="handleDelete(row)" v-hasPermi="['{{ moduleName }}:{{ businessName }}:remove']">删除</el-button>
                </template>
            </auto-table>
            <div class="table-pagination">
                <pagination
                    v-show="total > 0"
                    :total="total"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    @pagination="getList"
                />
            </div>
        </el-card>

    <!-- 添加或修改{{ functionName }}对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="{{ businessName }}Ref" :model="form" :rules="rules" label-width="80px">
        {% for column in columns %}
          {% set field = column.pythonField | snake_to_camel %}
          {% if column.isInsert == "1" and not column.isPk == "1" %}
            {% if column.usableColumn or not column.superColumn %}
              {% set parentheseIndex = column.columnComment.find("（") %}
              {% set comment = column.columnComment[:parentheseIndex] if parentheseIndex != -1 else column.columnComment %}
              {% set dictType = column.dictType %}

              {% if column.htmlType == "input" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-input v-model="form.{{ field }}" placeholder="请输入{{ comment }}" />
                </el-form-item>
              {% elif column.htmlType == "imageUpload" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <image-upload v-model="form.{{ field }}"/>
                </el-form-item>
              {% elif column.htmlType == "fileUpload" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <file-upload v-model="form.{{ field }}"/>
                </el-form-item>
              {% elif column.htmlType == "editor" %}
                <el-form-item label="{{ comment }}">
                  <editor v-model="form.{{ field }}" :min-height="192"/>
                </el-form-item>
              {% elif column.htmlType == "select" and dictType != "" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-select v-model="form.{{ field }}" placeholder="请选择{{ comment }}">
                    <el-option
                      v-for="dict in {{ dictType }}"
                      :key="dict.value"
                      :label="dict.label"
                      {% if column.pythonType == "int" %}
                        :value="parseInt(dict.value)"
                      {% else %}
                        :value="dict.value"
                      {% endif %}
                    ></el-option>
                  </el-select>
                </el-form-item>
              {% elif column.htmlType == "select" and dictType %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-select v-model="form.{{ field }}" placeholder="请选择{{ comment }}">
                    <el-option label="请选择字典生成" value="" />
                  </el-select>
                </el-form-item>
              {% elif column.htmlType == "checkbox" and dictType != "" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-checkbox-group v-model="form.{{ field }}">
                    <el-checkbox
                      v-for="dict in {{ dictType }}"
                      :key="dict.value"
                      :label="dict.value">
                      {{ dict.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              {% elif column.htmlType == "checkbox" and dictType %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-checkbox-group v-model="form.{{ field }}">
                    <el-checkbox>请选择字典生成</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              {% elif column.htmlType == "radio" and dictType != "" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-radio-group v-model="form.{{ field }}">
                    <el-radio
                      v-for="dict in {{ dictType }}"
                      :key="dict.value"
                      {% if column.pythonType == "int" %}
                        :label="parseInt(dict.value)"
                      {% else %}
                        :label="dict.value"
                      {% endif %}
                    >{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              {% elif column.htmlType == "radio" and dictType %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-radio-group v-model="form.{{ field }}">
                    <el-radio label="1">请选择字典生成</el-radio>
                  </el-radio-group>
                </el-form-item>
              {% elif column.htmlType == "datetime" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-date-picker clearable
                    v-model="form.{{ field }}"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择{{ comment }}">
                  </el-date-picker>
                </el-form-item>
              {% elif column.htmlType == "textarea" %}
                <el-form-item label="{{ comment }}" prop="{{ field }}">
                  <el-input v-model="form.{{ field }}" type="textarea" placeholder="请输入内容" />
                </el-form-item>
              {% endif %}
            {% endif %}
          {% endif %}
        {% endfor %}

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
      <!-- 导入数据对话框 -->
    <ImportData
        v-if="openImport"
        v-model="openImport"
        tableName="{{ tableName }}"
        @success="handleImportSuccess"
    />
  </div>
</template>

<script setup name="{{ tableName|snake_to_pascal_case }}">
import { list{{ BusinessName }}, get{{ BusinessName }}, del{{ BusinessName }}, add{{ BusinessName }}, update{{ BusinessName }}, import{{ BusinessName }} } from "@/api/{{ moduleName }}/{{ businessName }}";
import { listAllTable } from '@/api/system/table'
import TableSetup from '@/components/TableSetup'
import AutoTable from '@/components/AutoTable'
import ImportData from '@/components/ImportData'
const { proxy } = getCurrentInstance();
{% if dicts != '' %}
{% set dictsNoSymbol = dicts.replace("'", "") %}
const { {{ dictsNoSymbol }} } = proxy.useDict({{ dicts }});
{% endif %}

const {{ businessName }}List = ref([]);
{#{% if table.sub %}#}
{#  const {{ subclassName }}List = ref([]);#}
{#{% endif %}#}
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
{#{% if table.sub %}#}
{#  const checked{{ subClassName }} = ref([]);#}
{#{% endif %}#}
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
{% for column in columns %}
  {% if column.htmlType == "datetime" and column.queryType == "BETWEEN" %}
    const daterange{{ column.pythonField | snake_to_pascal_case }} = ref([]);
  {% endif %}
{% endfor %}

const columns = ref([])
const stripe = ref(true)
const isTable = ref(true)
const tableHeight = ref(500)
const fullScreen = ref(false)
const openImport = ref(false)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    {% for column in columns %}
    {% if column.isQuery == "1" %}
    {{ column.pythonField | snake_to_camel }}: null{% if not loop.last %},{% endif %}

    {% endif %}
    {% endfor %}
  },
  rules: {
    {% for column in columns %}
      {% if column.isRequired == "1" %}
        {% set parentheseIndex = column.columnComment.find("（") %}
        {% set comment = column.columnComment[:parentheseIndex] if parentheseIndex != -1 else column.columnComment %}
        {{ column.pythonField | snake_to_camel }}: [
          { required: true, message: "{{ comment }}不能为空", trigger: "{% if column.htmlType == "select" or column.htmlType == "radio" %}change{% else %}blur{% endif %}" }
        ]{% if not loop.last %},{% endif %}
      {% endif %}
    {% endfor %}
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询{{ functionName }}列表 */
function getList() {
  loading.value = true;
  {% for column in columns %}
    {% if column.htmlType == "datetime" and column.queryType == "BETWEEN" %}
      queryParams.value.params = {};
    {% endif %}
  {% endfor %}
  {% for column in columns %}
    {% if column.htmlType == "datetime" and column.queryType == "BETWEEN" %}
      if (null != daterange{{ column.pythonField | snake_to_pascal_case }} && '' != daterange{{ column.pythonField | snake_to_pascal_case }}) {
        queryParams.value.params["begin{{ column.pythonField | snake_to_pascal_case }}"] = daterange{{ column.pythonField | snake_to_pascal_case }}.value[0];
        queryParams.value.params["end{{ column.pythonField | snake_to_pascal_case }}"] = daterange{{ column.pythonField | snake_to_pascal_case }}.value[1];
      }
    {% endif %}
  {% endfor %}
  list{{ BusinessName }}(queryParams.value).then(response => {
    {{ businessName }}List.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function getColumns() {
    listAllTable({ tableName: '{{ tableName }}' })
        .then((response) => {
            columns.value = response.data
        })
        .then(() => {
            getList()
        })
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    {% for column in columns %}
      {% if column.htmlType == "checkbox" %}
        {{ column.pythonField | snake_to_camel  }}: []{% if not loop.last %},{% endif %}
      {% else %}
        {{ column.pythonField | snake_to_camel  }}: null{% if not loop.last %},{% endif %}
      {% endif %}
    {% endfor %}
  };
{#  {% if table.sub %}#}
{#    {{ subclassName }}List.value = [];#}
{#  {% endif %}#}
  proxy.resetForm("{{ businessName }}Ref");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  {% for column in columns %}
    {% if column.htmlType == "datetime" and column.queryType == "BETWEEN" %}
      daterange{{ column.pythonField | snake_to_pascal_case }}.value = [];
    {% endif %}
  {% endfor %}
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.{{ pkColumn.pythonField }});
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加{{ functionName }}";
}

/** 新增按钮操作 */
function handleImport() {
    openImport.value = true
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const {{ tableName | snake_to_camel }}{{ pkColumn.pythonField | snake_to_pascal_case }} = row.{{ pkColumn.pythonField | snake_to_camel }} || ids.value
  get{{ BusinessName }}({{ tableName | snake_to_camel }}{{ pkColumn.pythonField | snake_to_pascal_case }}).then(response => {
    form.value = response.data;
    {% for column in columns %}
      {% if column.htmlType == "checkbox" %}
        form.value.{{ column.pythonField | snake_to_camel }} = form.value.{{ column.pythonField | snake_to_camel }}.split(",");
      {% endif %}
    {% endfor %}
{#    {% if table.sub %}#}
{#      {{ subclassName }}List.value = response.data.{{ subclassName }}List;#}
{#    {% endif %}#}
    open.value = true;
    title.value = "修改{{ functionName }}";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["{{ businessName }}Ref"].validate(valid => {
    if (valid) {
      {% for column in columns %}
        {% if column.htmlType == "checkbox" %}
          form.value.{{ column.pythonField | snake_to_camel }} = form.value.{{ column.pythonField | snake_to_camel }}.join(",");
        {% endif %}
      {% endfor %}
{#      {% if table.sub %}#}
{#        form.value.{{ subclassName }}List = {{ subclassName }}List.value;#}
{#      {% endif %}#}
      if (form.value.{{ pkColumn.pythonField }} != null) {
        update{{ BusinessName }}(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        add{{ BusinessName }}(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _{{ pkColumn.pythonField }}s = row.{{ pkColumn.pythonField }} || ids.value;
  proxy.$modal.confirm('是否确认删除{{ functionName }}编号为"' + _{{ pkColumn.pythonField }}s + '"的数据项？').then(function() {
    return del{{ BusinessName }}(_{{ pkColumn.pythonField }}s);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

{#{% if table.sub %}#}
{#  /** {{ subTable.functionName }}序号 */#}
{#  function row{{ subClassName }}Index({ row, rowIndex }) {#}
{#    row.index = rowIndex + 1;#}
{#  }#}
{##}
{#  /** {{ subTable.functionName }}添加按钮操作 */#}
{#  function handleAdd{{ subClassName }}() {#}
{#    let obj = {};#}
{#    {% for column in subTable.columns %}#}
{#      {% if column.pk or column.pythonField == subTableFkclassName %}#}
{#      {% elif column.list and pythonField != "" %}#}
{#        obj.{{ column.pythonField }} = "";#}
{#      {% endif %}#}
{#    {% endfor %}#}
{#    {{ subclassName }}List.value.push(obj);#}
{#  }#}
{##}
{#  /** {{ subTable.functionName }}删除按钮操作 */#}
{#  function handleDelete{{ subClassName }}() {#}
{#    if (checked{{ subClassName }}.value.length == 0) {#}
{#      proxy.$modal.msgError("请先选择要删除的{{ subTable.functionName }}数据");#}
{#    } else {#}
{#      const {{ subclassName }}s = {{ subclassName }}List.value;#}
{#      const checked{{ subClassName }}s = checked{{ subClassName }}.value;#}
{#      {{ subclassName }}List.value = {{ subclassName }}s.filter(function(item) {#}
{#        return checked{{ subClassName }}s.indexOf(item.index) == -1#}
{#      });#}
{#    }#}
{#  }#}
{##}
{#  /** 复选框选中数据 */#}
{#  function handle{{ subClassName }}SelectionChange(selection) {#}
{#    checked{{ subClassName }}.value = selection.map(item => item.index)#}
{#  }#}
{#{% endif %}#}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('{{ moduleName }}/{{ businessName }}/export', {
    ...queryParams.value
  }, `{{ businessName }}_${new Date().getTime()}.xlsx`)
}

//表格全屏
function onfullTable() {
    proxy.$refs.tSetup.onFull(proxy.$refs.fullTable.$el)
    fullScreen.value = !fullScreen.value
    updateTableHeight()
}
//表格刷新
function onRefresh() {
    getList()
}
//搜索框显示隐藏
function onSearchChange() {
    showSearch.value = !showSearch.value
}

function onStripe(val) {
    stripe.value = val
}
//改变表头数据
function onChange(val) {
    columns.value = val
}

//改变表格宽度
function onColumnWidthChange(column) {
    proxy.$refs.tSetup.tableWidth(column)
}

//更新表格高度
function updateTableHeight() {
    if (
        proxy.$refs.tSetup &&
        proxy.$refs.queryRef &&
        document.querySelector('.table-pagination')
    ) {
        if (fullScreen.value) {
            tableHeight.value = window.innerHeight - 145
        } else {
            tableHeight.value =
                window.innerHeight -
                proxy.$refs.tSetup.$el.clientHeight -
                proxy.$refs.queryRef.$el.clientHeight -
                document.querySelector('.table-pagination').clientHeight -
                220
        }
    }
}
//导入成功
function handleImportSuccess(sheetName, filedInfo, fileName) {
    let data = {
        tableName: '{{ tableName }}',
        filedInfo: filedInfo,
        fileName: fileName,
        sheetName: sheetName
    }
    import{{ BusinessName }}(data).then(() => {
        proxy.$modal.msgSuccess('导入成功')
        openImport.value = false
        getList()
    })
    getList()
}

onMounted(() => {
    updateTableHeight() // 初始化计算高度
    window.addEventListener('resize', updateTableHeight) // 监听窗口大小变化
})

onUnmounted(() => {
    window.removeEventListener('resize', updateTableHeight) // 销毁监听
})

getColumns()

</script>
