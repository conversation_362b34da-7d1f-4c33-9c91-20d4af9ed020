import request from '@/utils/request'

// 查询{{functionName}}列表
export function list{{BusinessName}}(query) {
  return request({
    url: '/{{moduleName}}/{{businessName}}/list',
    method: 'get',
    params: query
  })
}

// 查询{{functionName}}详细
export function get{{BusinessName}}({{pkColumn.pythonField | snake_to_camel}}) {
  return request({
    url: '/{{moduleName}}/{{businessName}}/getById/' + {{pkColumn.pythonField | snake_to_camel}},
    method: 'get'
  })
}

// 新增{{functionName}}
export function add{{BusinessName}}(data) {
  return request({
    url: '/{{moduleName}}/{{businessName}}/add',
    method: 'post',
    data: data
  })
}

// 修改{{functionName}}
export function update{{BusinessName}}(data) {
  return request({
    url: '/{{moduleName}}/{{businessName}}/update',
    method: 'put',
    data: data
  })
}

// 删除{{functionName}}
export function del{{BusinessName}}({{pkColumn.pythonField | snake_to_camel}}) {
  return request({
    url: '/{{moduleName}}/{{businessName}}/delete/' + {{pkColumn.pythonField | snake_to_camel}},
    method: 'delete'
  })
}

// 导入{{functionName}}
export function import{{BusinessName}}(data) {
    return request({
      url: '/{{moduleName}}/{{businessName}}/import',
      method: 'post',
      data: data
    })
}
