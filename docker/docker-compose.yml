version: '3'

networks:
  app:
    driver: bridge

services:

  flux-nginx:
    container_name: flux-nginx
    image: nginx:latest
    restart: always
    depends_on:
      flux-fastapi:
        condition: service_started
    volumes:
      - ../flux-frontend/dist:/public/admin
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/conf.d:/etc/nginx/conf.d
      - ./log/nginx:/logs
    networks:
      - app
    ports:
      - "80:80"
      - "6379:6379"
      - "3306:3306"

  flux-fastapi:
    depends_on:
      - flux-mysql
      - flux-redis
    container_name: flux-server
    build:
      context: ./
      dockerfile: ./build/python/Dockerfile
      args:
        # 可以在这里添加构建参数，例如版本号等
        APP_VERSION: 1.0
      tags:
        - fastapi:1.0
    restart: always
    tty: true
    working_dir: /app/server
    volumes:
      - ../flux-backend:/app/server
      - ./log/server:/logs
    networks:
      - app
    command: stdbuf -oL python /app/server/app.py --env=dev 2>&1 | tee /logs/server.log

  flux-mysql:
    container_name: flux-mysql
    image: mysql:5.7.43
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
    volumes:
      - ../flux-backend/sql:/docker-entrypoint-initdb.d
      - ./data/mysql-5.7.43/lib:/var/lib/mysql
      - ./data/mysql-5.7.43/log/:/var/log/mysql/
      - ./config/mysql/mysqld.cnf:/etc/mysql/my.cnf
    networks:
      - app
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 5s
      timeout: 3s
      retries: 10

  flux-redis:
    container_name: flux-redis
    image: redis:7.0.11  # 指定具体版本号
    restart: always
    volumes:
      - ./data/redis:/data
    networks:
      - app