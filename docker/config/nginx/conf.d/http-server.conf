server {
    listen 80;
    access_log /logs/access_nginx.log;
    error_log /logs/error_nginx.log;
    client_max_body_size 5M;

    # 根路径重定向到 /admin/
    location = / {
        return 301 /admin/;
    }

    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    location /admin/ {
        alias   /public/admin/;
        index  index.html index.htm index.php;
    }

    # 后台后端
    location /server/ {
        proxy_pass http://flux-server:9099;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 文档，可注释去掉
    location /docs {
        proxy_pass http://flux-server:9099/docs;
        proxy_set_header Origin "";
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP $remote_addr;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /var/www/html;
    }
}